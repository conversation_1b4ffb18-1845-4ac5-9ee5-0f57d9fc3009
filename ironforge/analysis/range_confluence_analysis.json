{
  "analysis_metadata": {
    "timestamp": "2025-08-15T16:36:28.800117",
    "total_patterns_analyzed": 560,
    "range_levels_analyzed": [
      "20%",
      "60%",
      "40%",
      "80%"
    ],
    "analysis_scope": "Range Position Confluence Behavioral Analysis"
  },
  "range_level_analysis": {
    "20%": {
      "range_level": "20%",
      "total_patterns": 152,
      "session_phase_timing": {
        "phase_distribution": {
          "session_closing": 152
        },
        "session_position_stats": [
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          5.0,
          5.0,
          5.0,
          5.0,
          4.0,
          4.0,
          4.0,
          5.0,
          4.0,
          5.0,
          4.0,
          5.0,
          5.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          4.0,
          4.0,
          5.0,
          5.0,
          5.0,
          1.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          5.0,
          5.0,
          5.0,
          5.0,
          4.0,
          4.0,
          4.0,
          5.0,
          4.0,
          5.0,
          4.0,
          5.0,
          5.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          4.0,
          4.0,
          5.0,
          5.0,
          5.0,
          1.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0
        ],
        "timing_characteristics": {
          "avg_session_position": 2.6578947368421053,
          "median_session_position": 1.0,
          "position_std": 1.8676796156767372,
          "early_occurrence_rate": 0.5526315789473685,
          "mid_occurrence_rate": 0.0,
          "late_occurrence_rate": 0.4473684210526316
        }
      },
      "range_completion_rates": {
        "current_level": "20%",
        "completion_probability_80pct": 0.0,
        "completion_probability_100pct": 0.0,
        "session_success_metrics": {
          "avg_phase_significance": 0.9,
          "high_significance_rate": 1.0,
          "success_consistency": 1.0
        },
        "range_progression_analysis": {
          "avg_range_reached": 28.142105263157898,
          "max_range_reached": 38.2,
          "min_range_reached": 20.0,
          "range_std": 9.04944405326262,
          "progression_beyond_current": 0.4473684210526316
        }
      },
      "htf_confluence_characteristics": {
        "confluence_strength": {
          "pattern_strength": [
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5
          ],
          "confluence_rate": 1.0
        },
        "timeframe_interactions": {
          "1m": 152
        },
        "structural_characteristics": {
          "avg_confluence_strength": 0.5,
          "strong_confluence_rate": 0.0,
          "confluence_consistency": 1.0
        }
      },
      "session_energy_signatures": {
        "energy_distribution": {},
        "session_characteristics": {
          "dominant_phase": "session_closing",
          "avg_phase_significance": 0.9,
          "high_energy_phase_rate": 1.0,
          "energy_consistency": 1.0
        },
        "energy_patterns": {}
      },
      "velocity_patterns": {
        "temporal_velocity": {
          "avg_session_position": 2.6578947368421053,
          "position_velocity_spread": 1.8676796156767372,
          "early_velocity_patterns": 0.5526315789473685,
          "late_velocity_patterns": 0.3157894736842105
        },
        "structural_velocity": {
          "avg_range_position": 28.142105263157898,
          "range_velocity_spread": 9.04944405326262,
          "velocity_consistency": 0.6784375593566677
        },
        "progression_patterns": {
          "dominant_progression_phase": "session_closing",
          "avg_progression_significance": 0.9,
          "high_velocity_rate": 1.0
        }
      },
      "cross_session_continuation": {
        "continuation_probability": 1.0,
        "session_persistence": {
          "unique_sessions": 1,
          "avg_patterns_per_session": 152.0,
          "max_session_persistence": 152,
          "session_distribution": {
            "unknown": 152
          }
        },
        "evolution_tracking": {
          "total_evolution_links": 150,
          "evolution_stage_distribution": {
            "early_evolution": 51,
            "mid_evolution": 50,
            "late_evolution": 49
          },
          "avg_evolution_strength": 0.9226666666666665,
          "strong_evolution_rate": 0.6133333333333333
        },
        "predictive_metrics": {
          "structural_consistency": 1.0,
          "high_consistency_rate": 1.0,
          "predictive_reliability": 75.5
        }
      }
    },
    "60%": {
      "range_level": "60%",
      "total_patterns": 118,
      "session_phase_timing": {
        "phase_distribution": {
          "session_closing": 118
        },
        "session_position_stats": [
          1.0,
          5.0,
          5.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          1.0,
          1.0,
          5.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          4.0,
          5.0,
          5.0,
          4.0,
          5.0,
          4.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          5.0,
          4.0,
          1.0,
          5.0,
          4.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          4.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          1.0,
          1.0,
          5.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          4.0,
          5.0,
          5.0,
          4.0,
          5.0,
          4.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0,
          5.0,
          4.0,
          1.0,
          5.0,
          4.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          4.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          5.0
        ],
        "timing_characteristics": {
          "avg_session_position": 2.847457627118644,
          "median_session_position": 2.5,
          "position_std": 1.8759274255236147,
          "early_occurrence_rate": 0.5,
          "mid_occurrence_rate": 0.0,
          "late_occurrence_rate": 0.5
        }
      },
      "range_completion_rates": {
        "current_level": "60%",
        "completion_probability_80pct": 0.0,
        "completion_probability_100pct": 0.0,
        "session_success_metrics": {
          "avg_phase_significance": 0.9000000000000005,
          "high_significance_rate": 1.0,
          "success_consistency": 0.9999999999999996
        },
        "range_progression_analysis": {
          "avg_range_reached": 70.5864406779661,
          "max_range_reached": 78.0,
          "min_range_reached": 61.8,
          "range_std": 8.070861118594067,
          "progression_beyond_current": 1.0
        }
      },
      "htf_confluence_characteristics": {
        "confluence_strength": {
          "pattern_strength": [
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5
          ],
          "confluence_rate": 1.0
        },
        "timeframe_interactions": {
          "1m": 118
        },
        "structural_characteristics": {
          "avg_confluence_strength": 0.5,
          "strong_confluence_rate": 0.0,
          "confluence_consistency": 1.0
        }
      },
      "session_energy_signatures": {
        "energy_distribution": {},
        "session_characteristics": {
          "dominant_phase": "session_closing",
          "avg_phase_significance": 0.9000000000000005,
          "high_energy_phase_rate": 1.0,
          "energy_consistency": 0.9999999999999996
        },
        "energy_patterns": {}
      },
      "velocity_patterns": {
        "temporal_velocity": {
          "avg_session_position": 2.847457627118644,
          "position_velocity_spread": 1.8759274255236147,
          "early_velocity_patterns": 0.5,
          "late_velocity_patterns": 0.3474576271186441
        },
        "structural_velocity": {
          "avg_range_position": 70.5864406779661,
          "range_velocity_spread": 8.070861118594067,
          "velocity_consistency": 0.8856598938680665
        },
        "progression_patterns": {
          "dominant_progression_phase": "session_closing",
          "avg_progression_significance": 0.9000000000000005,
          "high_velocity_rate": 1.0
        }
      },
      "cross_session_continuation": {
        "continuation_probability": 1.0,
        "session_persistence": {
          "unique_sessions": 1,
          "avg_patterns_per_session": 118.0,
          "max_session_persistence": 118,
          "session_distribution": {
            "unknown": 118
          }
        },
        "evolution_tracking": {
          "total_evolution_links": 116,
          "evolution_stage_distribution": {
            "early_evolution": 39,
            "mid_evolution": 40,
            "late_evolution": 37
          },
          "avg_evolution_strength": 0.9275862068965517,
          "strong_evolution_rate": 0.6379310344827587
        },
        "predictive_metrics": {
          "structural_consistency": 1.0,
          "high_consistency_rate": 1.0,
          "predictive_reliability": 58.5
        }
      }
    },
    "40%": {
      "range_level": "40%",
      "total_patterns": 68,
      "session_phase_timing": {
        "phase_distribution": {
          "session_closing": 68
        },
        "session_position_stats": [
          1.0,
          5.0,
          5.0,
          1.0,
          1.0,
          5.0,
          5.0,
          1.0,
          1.0,
          4.0,
          5.0,
          5.0,
          4.0,
          5.0,
          4.0,
          5.0,
          1.0,
          1.0,
          5.0,
          4.0,
          5.0,
          1.0,
          5.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          1.0,
          1.0,
          5.0,
          5.0,
          1.0,
          1.0,
          4.0,
          5.0,
          5.0,
          4.0,
          5.0,
          4.0,
          5.0,
          1.0,
          1.0,
          5.0,
          4.0,
          5.0,
          1.0,
          5.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          1.0,
          5.0
        ],
        "timing_characteristics": {
          "avg_session_position": 2.8823529411764706,
          "median_session_position": 4.0,
          "position_std": 1.8592330152093068,
          "early_occurrence_rate": 0.4852941176470588,
          "mid_occurrence_rate": 0.0,
          "late_occurrence_rate": 0.5147058823529411
        }
      },
      "range_completion_rates": {
        "current_level": "40%",
        "completion_probability_80pct": 0.0,
        "completion_probability_100pct": 0.0,
        "session_success_metrics": {
          "avg_phase_significance": 0.9,
          "high_significance_rate": 1.0,
          "success_consistency": 1.0
        },
        "range_progression_analysis": {
          "avg_range_reached": 50.0,
          "max_range_reached": 50.0,
          "min_range_reached": 50.0,
          "range_std": 0.0,
          "progression_beyond_current": 1.0
        }
      },
      "htf_confluence_characteristics": {
        "confluence_strength": {
          "pattern_strength": [
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5
          ],
          "confluence_rate": 1.0
        },
        "timeframe_interactions": {
          "1m": 68
        },
        "structural_characteristics": {
          "avg_confluence_strength": 0.5,
          "strong_confluence_rate": 0.0,
          "confluence_consistency": 1.0
        }
      },
      "session_energy_signatures": {
        "energy_distribution": {},
        "session_characteristics": {
          "dominant_phase": "session_closing",
          "avg_phase_significance": 0.9,
          "high_energy_phase_rate": 1.0,
          "energy_consistency": 1.0
        },
        "energy_patterns": {}
      },
      "velocity_patterns": {
        "temporal_velocity": {
          "avg_session_position": 2.8823529411764706,
          "position_velocity_spread": 1.8592330152093068,
          "early_velocity_patterns": 0.4852941176470588,
          "late_velocity_patterns": 0.3382352941176471
        },
        "structural_velocity": {
          "avg_range_position": 50.0,
          "range_velocity_spread": 0.0,
          "velocity_consistency": 1.0
        },
        "progression_patterns": {
          "dominant_progression_phase": "session_closing",
          "avg_progression_significance": 0.9,
          "high_velocity_rate": 1.0
        }
      },
      "cross_session_continuation": {
        "continuation_probability": 1.0,
        "session_persistence": {
          "unique_sessions": 1,
          "avg_patterns_per_session": 68.0,
          "max_session_persistence": 68,
          "session_distribution": {
            "unknown": 68
          }
        },
        "evolution_tracking": {
          "total_evolution_links": 67,
          "evolution_stage_distribution": {
            "early_evolution": 23,
            "mid_evolution": 22,
            "late_evolution": 22
          },
          "avg_evolution_strength": 0.8865671641791042,
          "strong_evolution_rate": 0.43283582089552236
        },
        "predictive_metrics": {
          "structural_consistency": 1.0,
          "high_consistency_rate": 1.0,
          "predictive_reliability": 33.5
        }
      }
    },
    "80%": {
      "range_level": "80%",
      "total_patterns": 62,
      "session_phase_timing": {
        "phase_distribution": {
          "session_closing": 62
        },
        "session_position_stats": [
          1.0,
          5.0,
          5.0,
          1.0,
          1.0,
          5.0,
          1.0,
          5.0,
          1.0,
          5.0,
          5.0,
          4.0,
          4.0,
          5.0,
          4.0,
          1.0,
          5.0,
          4.0,
          5.0,
          5.0,
          5.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          5.0,
          5.0,
          1.0,
          1.0,
          5.0,
          1.0,
          5.0,
          1.0,
          5.0,
          5.0,
          4.0,
          4.0,
          5.0,
          4.0,
          1.0,
          5.0,
          4.0,
          5.0,
          5.0,
          5.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          4.0,
          1.0,
          1.0,
          1.0,
          5.0
        ],
        "timing_characteristics": {
          "avg_session_position": 3.064516129032258,
          "median_session_position": 4.0,
          "position_std": 1.848023946761613,
          "early_occurrence_rate": 0.43548387096774194,
          "mid_occurrence_rate": 0.0,
          "late_occurrence_rate": 0.5645161290322581
        }
      },
      "range_completion_rates": {
        "current_level": "80%",
        "completion_probability_80pct": 1.0,
        "completion_probability_100pct": 0.0,
        "session_success_metrics": {
          "avg_phase_significance": 0.8999999999999999,
          "high_significance_rate": 1.0,
          "success_consistency": 0.9999999999999999
        },
        "range_progression_analysis": {
          "avg_range_reached": 85.0,
          "max_range_reached": 85.0,
          "min_range_reached": 85.0,
          "range_std": 0.0,
          "progression_beyond_current": 1.0
        }
      },
      "htf_confluence_characteristics": {
        "confluence_strength": {
          "pattern_strength": [
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5,
            0.5
          ],
          "confluence_rate": 1.0
        },
        "timeframe_interactions": {
          "1m": 62
        },
        "structural_characteristics": {
          "avg_confluence_strength": 0.5,
          "strong_confluence_rate": 0.0,
          "confluence_consistency": 1.0
        }
      },
      "session_energy_signatures": {
        "energy_distribution": {},
        "session_characteristics": {
          "dominant_phase": "session_closing",
          "avg_phase_significance": 0.8999999999999999,
          "high_energy_phase_rate": 1.0,
          "energy_consistency": 0.9999999999999999
        },
        "energy_patterns": {}
      },
      "velocity_patterns": {
        "temporal_velocity": {
          "avg_session_position": 3.064516129032258,
          "position_velocity_spread": 1.848023946761613,
          "early_velocity_patterns": 0.43548387096774194,
          "late_velocity_patterns": 0.3709677419354839
        },
        "structural_velocity": {
          "avg_range_position": 85.0,
          "range_velocity_spread": 0.0,
          "velocity_consistency": 1.0
        },
        "progression_patterns": {
          "dominant_progression_phase": "session_closing",
          "avg_progression_significance": 0.8999999999999999,
          "high_velocity_rate": 1.0
        }
      },
      "cross_session_continuation": {
        "continuation_probability": 1.0,
        "session_persistence": {
          "unique_sessions": 1,
          "avg_patterns_per_session": 62.0,
          "max_session_persistence": 62,
          "session_distribution": {
            "unknown": 62
          }
        },
        "evolution_tracking": {
          "total_evolution_links": 61,
          "evolution_stage_distribution": {
            "early_evolution": 21,
            "mid_evolution": 20,
            "late_evolution": 20
          },
          "avg_evolution_strength": 0.9213114754098359,
          "strong_evolution_rate": 0.6065573770491803
        },
        "predictive_metrics": {
          "structural_consistency": 1.0,
          "high_consistency_rate": 1.0,
          "predictive_reliability": 30.5
        }
      }
    },
    "cross_range_analysis": {
      "range_level_transitions": {
        "total_transitions": 9168,
        "unique_transition_pairs": 4,
        "most_common_transitions": {
          