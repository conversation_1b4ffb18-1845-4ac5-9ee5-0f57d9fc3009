#!/usr/bin/env python3
"""
Deep Dive Analysis of Top Convergence Node
==========================================

Detailed analysis of the NY_AM 2025-08-05 session showing 99% HTF carryover
and 98% cross-session inheritance with extreme multi-timeframe convergence.
"""

import json
import numpy as np
from datetime import datetime
from pathlib import Path

def analyze_top_convergence_node():
    """Deep dive into NY_AM 2025-08-05 convergence node"""
    
    session_file = "/Users/<USER>/IRONFORGE/enhanced_sessions_with_relativity/enhanced_rel_NY_AM_Lvl-1_2025_08_05.json"
    
    print("🔍 DEEP DIVE: NY_AM 2025-08-05 Multi-Timeframe Convergence")
    print("=" * 70)
    
    with open(session_file, 'r') as f:
        session_data = json.load(f)
    
    # Session basics
    metadata = session_data['session_metadata']
    print(f"📅 Session: {metadata['session_date']} {metadata['session_type'].upper()}")
    print(f"⏰ Time: {metadata['session_start']} - {metadata['session_end']}")
    print(f"⏱️  Duration: {metadata['session_duration']} minutes")
    
    # HTF Convergence Analysis
    contamination = session_data['contamination_analysis']
    htf_data = contamination['htf_contamination']
    inheritance = contamination['cross_session_inheritance']
    
    print(f"\n🔗 HTF CONVERGENCE METRICS:")
    print(f"  HTF Carryover Strength: {htf_data['htf_carryover_strength']:.1%} (EXTREME)")
    print(f"  Cross-Session Inheritance: {htf_data['cross_session_inheritance']:.1%}")
    print(f"  Energy Density: {session_data.get('energy_density', 0):.1%}")
    
    print(f"\n🌐 CROSS-SESSION INTERACTIONS:")
    print(f"  London FPFVG Interactions: {inheritance['london_fpfvg_interactions']}")
    print(f"  Premarket FPFVG Interactions: {inheritance['premarket_fpfvg_interactions']}")
    print(f"  Midnight FPFVG Interactions: {inheritance['midnight_fpfvg_interactions']}")
    print(f"  Asia FPFVG Interactions: {inheritance['asia_fpfvg_interactions']}")
    print(f"  Three-day References: {inheritance['three_day_references']}")
    print(f"  Previous Day PM Interactions: {inheritance['previous_day_pm_interactions']}")
    print(f"  Previous Day AM Interactions: {inheritance['previous_day_am_interactions']}")
    
    print(f"\n🧬 HISTORICAL INFLUENCES:")
    for key, value in htf_data.items():
        if value is True and 'influence' in key:
            print(f"  ✅ {key.replace('_', ' ').title()}")
    
    # Energy State Analysis
    energy_state = session_data.get('energy_state', {})
    print(f"\n⚡ ENERGY DYNAMICS:")
    print(f"  Energy Source: {energy_state.get('energy_source', 'unknown')}")
    print(f"  Energy Rate: {energy_state.get('energy_rate', 0):.1f}")
    print(f"  Total Accumulated: {energy_state.get('total_accumulated', 0):.1f}")
    print(f"  Phase Transitions: {energy_state.get('phase_transitions', 0)}")
    print(f"  Expansion Phases: {energy_state.get('expansion_phases', 0)}")
    print(f"  Retracement Phases: {energy_state.get('retracement_phases', 0)}")
    print(f"  Consolidation Phases: {energy_state.get('consolidation_phases', 0)}")
    
    # Temporal Flow Analysis
    temporal_flow = session_data.get('temporal_flow_analysis', {})
    if temporal_flow:
        print(f"\n🌊 TEMPORAL FLOW PATTERNS:")
        
        if 'energy_accumulation' in temporal_flow:
            energy_acc = temporal_flow['energy_accumulation']
            print(f"  Energy Accumulation Rate: {energy_acc.get('energy_rate', 0):.3f}")
            print(f"  Total Energy Accumulated: {energy_acc.get('total_accumulated', 0):.1f}")
            print(f"  Accumulation Phase: {energy_acc.get('accumulation_phase', 'unknown')}")
            print(f"  Efficiency Factor: {energy_acc.get('efficiency_factor', 0):.3f}")
        
        if 'temporal_momentum_strength' in temporal_flow:
            print(f"  Temporal Momentum: {temporal_flow['temporal_momentum_strength']:.3f}")
        
        if 'persistence_factor' in temporal_flow:
            print(f"  Persistence Factor: {temporal_flow['persistence_factor']:.3f}")
    
    # Multi-Timeframe Price Action Analysis
    price_movements = session_data.get('price_movements', [])
    
    print(f"\n📊 MULTI-TIMEFRAME PRICE ACTION:")
    print(f"  Total Price Events: {len(price_movements)}")
    
    # Analyze momentum distribution
    momentum_events = [p for p in price_movements if 'price_momentum' in p and p['price_momentum'] != 0]
    if momentum_events:
        momentums = [p['price_momentum'] for p in momentum_events]
        print(f"  Momentum Events: {len(momentum_events)}")
        print(f"  Max Momentum: {max(momentums):.3f}")
        print(f"  Min Momentum: {min(momentums):.3f}")
        print(f"  Avg Momentum: {np.mean(momentums):.3f}")
    
    # Range position analysis
    range_positions = [p.get('range_position', 0) for p in price_movements if 'range_position' in p]
    if range_positions:
        print(f"  Range Coverage: {min(range_positions):.1%} - {max(range_positions):.1%}")
        print(f"  Avg Range Position: {np.mean(range_positions):.1%}")
    
    # Identify significant convergence moments
    print(f"\n🎯 CONVERGENCE MOMENTS:")
    
    # High momentum events (potential 1m convergence)
    high_momentum = [p for p in price_movements if abs(p.get('price_momentum', 0)) > 0.3]
    if high_momentum:
        print(f"  High Momentum Events (1m scale): {len(high_momentum)}")
        for i, event in enumerate(high_momentum[:3]):  # Top 3
            print(f"    {i+1}. {event['timestamp']}: {event['price_momentum']:.3f} momentum at {event['price_level']}")
    
    # Range extremes (potential 5m/15m convergence)
    range_extremes = [p for p in price_movements if p.get('range_position', 0.5) < 0.1 or p.get('range_position', 0.5) > 0.9]
    if range_extremes:
        print(f"  Range Extreme Events (5m/15m scale): {len(range_extremes)}")
        for i, event in enumerate(range_extremes[:3]):  # Top 3
            print(f"    {i+1}. {event['timestamp']}: {event['range_position']:.1%} range at {event['price_level']}")
    
    # FPFVG formation and interactions (1h+ scale)
    fpfvg_data = session_data.get('session_fpfvg', {})
    if fpfvg_data.get('fpfvg_present'):
        formation = fpfvg_data['fpfvg_formation']
        print(f"  FPFVG Formation (1h+ scale):")
        print(f"    Formation Time: {formation['formation_time']}")
        print(f"    Gap Size: {formation['gap_size']} points")
        print(f"    Premium High: {formation['premium_high']}")
        print(f"    Discount Low: {formation['discount_low']}")
        
        interactions = formation.get('interactions', [])
        print(f"    Interactions: {len(interactions)}")
        
        for i, interaction in enumerate(interactions[:5]):  # Top 5
            print(f"      {i+1}. {interaction['interaction_time']}: {interaction['interaction_type']} at {interaction['price_level']}")
    
    # Session liquidity events
    liquidity_events = session_data.get('session_liquidity_events', [])
    if liquidity_events:
        print(f"\n💧 LIQUIDITY EVENTS:")
        for i, event in enumerate(liquidity_events[:5]):  # Top 5
            timestamp = event.get('timestamp', 'unknown')
            event_type = event.get('event_type', 'unknown')
            intensity = event.get('intensity', 0)
            price_level = event.get('price_level', 0)
            print(f"  {i+1}. {timestamp}: {event_type} (intensity: {intensity:.3f}) at {price_level}")
    
    # Session statistics
    relativity_stats = session_data.get('relativity_stats', {})
    if relativity_stats:
        print(f"\n📈 SESSION STATISTICS:")
        print(f"  Session High: {relativity_stats.get('session_high', 0)}")
        print(f"  Session Low: {relativity_stats.get('session_low', 0)}")
        print(f"  Session Range: {relativity_stats.get('session_range', 0)} points")
        print(f"  Open: {relativity_stats.get('session_open', 0)}")
        print(f"  Close: {relativity_stats.get('session_close', 0)}")
    
    print(f"\n🔗 CONVERGENCE ANALYSIS SUMMARY:")
    print(f"This session represents EXTREME multi-timeframe convergence with:")
    print(f"  • 99% HTF carryover (maximum observed)")
    print(f"  • 98% cross-session inheritance")
    print(f"  • 95% energy density")
    print(f"  • {inheritance['london_fpfvg_interactions'] + inheritance['premarket_fpfvg_interactions'] + inheritance['midnight_fpfvg_interactions'] + inheritance['asia_fpfvg_interactions']} total cross-session FPFVG interactions")
    print(f"  • Three-day historical influence")
    print(f"  • 24 phase transitions indicating complex temporal dynamics")
    print(f"  • Energy source: extreme_historical_cross_session_interaction")
    
    print(f"\n🎯 This represents a perfect example of 1m/5m/15m/1h+ convergence where:")
    print(f"   - 1m events: High momentum price action")
    print(f"   - 5m events: Range extreme positioning")  
    print(f"   - 15m events: Phase transitions and energy accumulation")
    print(f"   - 1h+ events: Multi-session FPFVG inheritance and historical contamination")
    
    return session_data

if __name__ == "__main__":
    analyze_top_convergence_node()