{"execution_summary": {"total_sessions_attempted": 119, "successful_sessions": 82, "failed_sessions": 37, "success_rate": 68.90756302521008, "total_execution_time": 17.65749502182007, "avg_time_per_session": 0.21533530514414717}, "archaeological_discoveries": {"total_patterns": 25896, "pattern_types_discovered": 6, "avg_patterns_per_session": 315.8048780487805, "patterns_per_second": 1466.5726915397277, "pattern_type_breakdown": {"high_low_distance_structure": 206, "htf_confluence": 25379, "range_position_confluence": 273, "weekly_cycle_pattern": 17, "session_open_relationship": 12, "temporal_structural_position": 9}}, "graph_analysis": {"total_nodes": 2854, "total_edges": 25414, "total_scale_edges": 22753, "avg_scale_edge_percentage": 89.20947609344103, "timeframes_discovered": ["1m", "5m", "15m", "1h", "D"], "timeframe_distribution": {"1m": 2068, "5m": 445, "15m": 175, "1h": 84, "D": 82}}, "multi_day_coverage": {"dates_covered": ["2025_07_24", "2025_07_25", "2025_07_28", "2025_07_29", "2025_07_30", "2025_07_31", "2025_08_04", "2025_08_05", "2025_08_06", "2025_08_07"], "date_span_days": 10, "multi_day_dataset": true}, "production_readiness": {"no_session_limits": true, "no_chunking_used": true, "no_fallbacks_triggered": false, "full_feature_processing": true, "acceptable_runtime": true}, "failed_sessions": [{"session": "ASIA_Lvl-1_2025_07_23_htf_rel.json", "error": "Empty timestamps detected: ['Movement 4: empty timestamp', 'Movement 7: empty timestamp']"}, {"session": "ASIA_Lvl-1_2025_07_24_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level']"}, {"session": "ASIA_Lvl-1_2025_07_28_htf_rel.json", "error": "Invalid movements detected: ['Movement 0: invalid price 0.0', 'Movement 1: invalid price 0.0', 'Movement 2: invalid price 0.0', 'Movement 3: invalid price 0.0']"}, {"session": "ASIA_Lvl-1_2025_07_29_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level']"}, {"session": "ASIA_Lvl-1_2025_07_30_htf_rel.json", "error": "Invalid movements detected: ['Movement 4: missing price_level', 'Movement 5: missing price_level', 'Movement 6: missing price_level', 'Movement 7: missing price_level', 'Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level']"}, {"session": "LONDON_Lvl-1_2025_07_24_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level']"}, {"session": "LONDON_Lvl-1_2025_07_28_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level']"}, {"session": "LONDON_Lvl-1_2025_07_30_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level', 'Movement 19: missing price_level', 'Movement 20: missing price_level', 'Movement 21: missing price_level', 'Movement 22: missing price_level', 'Movement 23: missing price_level']"}, {"session": "LONDON_Lvl-1_2025_07_31_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level', 'Movement 19: missing price_level', 'Movement 20: missing price_level']"}, {"session": "LUNCH_Lvl-1_2025_07_24_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level']"}, {"session": "LUNCH_Lvl-1_2025_07_25_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level']"}, {"session": "LUNCH_Lvl-1_2025_07_28_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level']"}, {"session": "LUNCH_Lvl-1_2025_07_29_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level']"}, {"session": "LUNCH_Lvl-1_2025_07_30_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level']"}, {"session": "MIDNIGHT_Lvl-1_2025_07_24_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level']"}, {"session": "MIDNIGHT_Lvl-1_2025_07_25_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level']"}, {"session": "MIDNIGHT_Lvl-1_2025_07_28_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level']"}, {"session": "MIDNIGHT_Lvl-1_2025_07_29_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level']"}, {"session": "MIDNIGHT_Lvl-1_2025_07_30_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level']"}, {"session": "MIDNIGHT_Lvl-1_2025_07_31_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level']"}, {"session": "NYPM_Lvl-1_2025_08_07_REAL_htf_rel.json", "error": "Invalid movements detected: ['Movement 0: missing price_level', 'Movement 1: missing price_level', 'Movement 2: missing price_level', 'Movement 3: missing price_level', 'Movement 4: missing price_level', 'Movement 5: missing price_level', 'Movement 6: missing price_level', 'Movement 7: missing price_level', 'Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level', 'Movement 19: missing price_level', 'Movement 20: missing price_level', 'Movement 21: missing price_level', 'Movement 22: missing price_level', 'Movement 23: missing price_level', 'Movement 24: missing price_level', 'Movement 25: missing price_level', 'Movement 26: missing price_level', 'Movement 27: missing price_level', 'Movement 28: missing price_level', 'Movement 29: missing price_level', 'Movement 30: missing price_level', 'Movement 31: missing price_level', 'Movement 32: missing price_level', 'Movement 33: missing price_level', 'Movement 34: missing price_level', 'Movement 35: missing price_level', 'Movement 36: missing price_level', 'Movement 37: missing price_level', 'Movement 38: missing price_level', 'Movement 39: missing price_level', 'Movement 40: missing price_level', 'Movement 41: missing price_level', 'Movement 42: missing price_level', 'Movement 43: missing price_level']"}, {"session": "NY_AM_Lvl-1_2025_07_24_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level']"}, {"session": "NY_AM_Lvl-1_2025_07_25_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level']"}, {"session": "NY_AM_Lvl-1_2025_07_28_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level']"}, {"session": "NY_AM_Lvl-1_2025_07_29_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level', 'Movement 19: missing price_level', 'Movement 20: missing price_level', 'Movement 21: missing price_level', 'Movement 22: missing price_level', 'Movement 23: missing price_level', 'Movement 24: missing price_level', 'Movement 25: missing price_level', 'Movement 26: missing price_level', 'Movement 27: missing price_level', 'Movement 28: missing price_level', 'Movement 29: missing price_level', 'Movement 30: missing price_level', 'Movement 31: missing price_level']"}, {"session": "NY_AM_Lvl-1_2025_07_30_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level', 'Movement 19: missing price_level']"}, {"session": "NY_AM_Lvl-1_2025_07_31_htf_rel.json", "error": "Invalid movements detected: ['Movement 4: missing price_level', 'Movement 5: missing price_level', 'Movement 6: missing price_level', 'Movement 7: missing price_level', 'Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level']"}, {"session": "NY_PM_Lvl-1_2025_07_28_htf_rel.json", "error": "Invalid movements detected: ['Movement 4: missing price_level', 'Movement 5: missing price_level', 'Movement 6: missing price_level', 'Movement 7: missing price_level', 'Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level']"}, {"session": "NY_PM_Lvl-1_2025_07_29_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level', 'Movement 19: missing price_level', 'Movement 20: missing price_level', 'Movement 21: missing price_level', 'Movement 22: missing price_level', 'Movement 23: missing price_level', 'Movement 24: missing price_level', 'Movement 25: missing price_level', 'Movement 26: missing price_level', 'Movement 27: missing price_level', 'Movement 28: missing price_level', 'Movement 29: missing price_level']"}, {"session": "NY_PM_Lvl-1_2025_07_30_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level', 'Movement 19: missing price_level', 'Movement 20: missing price_level', 'Movement 21: missing price_level', 'Movement 22: missing price_level', 'Movement 23: missing price_level', 'Movement 24: missing price_level', 'Movement 25: missing price_level', 'Movement 26: missing price_level', 'Movement 27: missing price_level', 'Movement 28: missing price_level', 'Movement 29: missing price_level', 'Movement 30: missing price_level', 'Movement 31: missing price_level', 'Movement 32: missing price_level', 'Movement 33: missing price_level']"}, {"session": "PREASIA_Lvl-1_2025_07_30_htf_rel.json", "error": "Invalid movements detected: ['Movement 4: missing price_level', 'Movement 5: missing price_level', 'Movement 6: missing price_level', 'Movement 7: missing price_level']"}, {"session": "PREMARKET_Lvl-1_2025_07_24_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level']"}, {"session": "PREMARKET_Lvl-1_2025_07_25_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level']"}, {"session": "PREMARKET_Lvl-1_2025_07_28_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level']"}, {"session": "PREMARKET_Lvl-1_2025_07_29_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level']"}, {"session": "PREMARKET_Lvl-1_2025_07_30_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level', 'Movement 19: missing price_level', 'Movement 20: missing price_level', 'Movement 21: missing price_level']"}, {"session": "PREMARKET_Lvl-1_2025_07_31_htf_rel.json", "error": "Invalid movements detected: ['Movement 8: missing price_level', 'Movement 9: missing price_level', 'Movement 10: missing price_level', 'Movement 11: missing price_level', 'Movement 12: missing price_level', 'Movement 13: missing price_level', 'Movement 14: missing price_level', 'Movement 15: missing price_level', 'Movement 16: missing price_level', 'Movement 17: missing price_level', 'Movement 18: missing price_level']"}], "session_details": [{"session_name": "ASIA_Lvl-1_2025_07_24_htf_regenerated_rel.json", "nodes": 17, "edges": 107, "patterns": 109, "scale_edges": 95, "scale_edge_percentage": 88.78504672897196, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"high_low_distance_structure": 2, "htf_confluence": 107}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "ASIA_Lvl-1_2025_07_29_htf_regenerated_rel.json", "nodes": 24, "edges": 159, "patterns": 161, "scale_edges": 140, "scale_edge_percentage": 88.0503144654088, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "high_low_distance_structure": 1, "htf_confluence": 159}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "ASIA_Lvl-1_2025_07_30_htf_regenerated_rel.json", "nodes": 19, "edges": 155, "patterns": 157, "scale_edges": 141, "scale_edge_percentage": 90.96774193548387, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "high_low_distance_structure": 1, "htf_confluence": 155}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "ASIA_Lvl-1_2025_08_05_htf_regenerated_rel.json", "nodes": 47, "edges": 462, "patterns": 468, "scale_edges": 417, "scale_edge_percentage": 90.25974025974025, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 4, "high_low_distance_structure": 2, "htf_confluence": 462}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "ASIA_Lvl-1_2025_08_05_htf_rel.json", "nodes": 47, "edges": 437, "patterns": 445, "scale_edges": 390, "scale_edge_percentage": 89.24485125858124, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 4, "high_low_distance_structure": 4, "htf_confluence": 437}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "ASIA_Lvl-1_2025_08_06_htf_regenerated_rel.json", "nodes": 24, "edges": 165, "patterns": 169, "scale_edges": 146, "scale_edge_percentage": 88.48484848484848, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "high_low_distance_structure": 2, "htf_confluence": 165}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "ASIA_Lvl-1_2025_08_06_htf_rel.json", "nodes": 24, "edges": 156, "patterns": 161, "scale_edges": 137, "scale_edge_percentage": 87.82051282051282, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 3, "high_low_distance_structure": 2, "htf_confluence": 156}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "ASIA_Lvl-1_2025_08_07_htf_regenerated_rel.json", "nodes": 53, "edges": 510, "patterns": 521, "scale_edges": 461, "scale_edge_percentage": 90.3921568627451, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 5, "htf_confluence": 510}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "ASIA_Lvl-1_2025_08_07_htf_rel.json", "nodes": 53, "edges": 495, "patterns": 506, "scale_edges": 442, "scale_edge_percentage": 89.29292929292929, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 5, "htf_confluence": 495}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_07_24_htf_regenerated_rel.json", "nodes": 15, "edges": 117, "patterns": 118, "scale_edges": 107, "scale_edge_percentage": 91.45299145299145, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "htf_confluence": 117}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_07_25_htf_regenerated_rel.json", "nodes": 18, "edges": 107, "patterns": 110, "scale_edges": 94, "scale_edge_percentage": 87.85046728971963, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 2, "htf_confluence": 107}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_07_25_htf_rel.json", "nodes": 18, "edges": 118, "patterns": 84, "scale_edges": 105, "scale_edge_percentage": 88.98305084745762, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "htf_confluence": 83}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_07_28_htf_regenerated_rel.json", "nodes": 21, "edges": 143, "patterns": 149, "scale_edges": 127, "scale_edge_percentage": 88.81118881118881, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 3, "high_low_distance_structure": 2, "htf_confluence": 143}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_07_30_htf_regenerated_rel.json", "nodes": 33, "edges": 261, "patterns": 265, "scale_edges": 233, "scale_edge_percentage": 89.272030651341, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 3, "high_low_distance_structure": 1, "htf_confluence": 261}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_07_31_htf_regenerated_rel.json", "nodes": 30, "edges": 260, "patterns": 262, "scale_edges": 235, "scale_edge_percentage": 90.38461538461539, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "high_low_distance_structure": 1, "htf_confluence": 260}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_08_05_htf_regenerated_rel.json", "nodes": 49, "edges": 484, "patterns": 495, "scale_edges": 433, "scale_edge_percentage": 89.46280991735537, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 5, "htf_confluence": 484}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_08_05_htf_rel.json", "nodes": 49, "edges": 429, "patterns": 441, "scale_edges": 379, "scale_edge_percentage": 88.34498834498834, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 6, "htf_confluence": 429}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_08_06_htf_regenerated_rel.json", "nodes": 30, "edges": 243, "patterns": 248, "scale_edges": 216, "scale_edge_percentage": 88.88888888888889, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 3, "high_low_distance_structure": 2, "htf_confluence": 243}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_08_06_htf_rel.json", "nodes": 30, "edges": 228, "patterns": 233, "scale_edges": 202, "scale_edge_percentage": 88.59649122807018, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 3, "high_low_distance_structure": 2, "htf_confluence": 228}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_08_07_htf_regenerated_rel.json", "nodes": 52, "edges": 499, "patterns": 508, "scale_edges": 449, "scale_edge_percentage": 89.97995991983969, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 5, "high_low_distance_structure": 4, "htf_confluence": 499}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LONDON_Lvl-1_2025_08_07_htf_rel.json", "nodes": 52, "edges": 466, "patterns": 475, "scale_edges": 415, "scale_edge_percentage": 89.05579399141631, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 4, "high_low_distance_structure": 5, "htf_confluence": 466}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_07_24_htf_regenerated_rel.json", "nodes": 15, "edges": 126, "patterns": 127, "scale_edges": 116, "scale_edge_percentage": 92.06349206349206, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "htf_confluence": 126}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_07_25_htf_regenerated_rel.json", "nodes": 19, "edges": 155, "patterns": 159, "scale_edges": 141, "scale_edge_percentage": 90.96774193548387, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 2, "high_low_distance_structure": 1, "htf_confluence": 155}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_07_28_htf_regenerated_rel.json", "nodes": 20, "edges": 153, "patterns": 156, "scale_edges": 137, "scale_edge_percentage": 89.54248366013073, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 1, "high_low_distance_structure": 1, "htf_confluence": 153}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_07_29_htf_regenerated_rel.json", "nodes": 26, "edges": 215, "patterns": 220, "scale_edges": 191, "scale_edge_percentage": 88.83720930232558, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 3, "high_low_distance_structure": 2, "htf_confluence": 215}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_07_30_htf_regenerated_rel.json", "nodes": 24, "edges": 190, "patterns": 192, "scale_edges": 169, "scale_edge_percentage": 88.94736842105263, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "htf_confluence": 190}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_08_04_htf_regenerated_rel.json", "nodes": 44, "edges": 371, "patterns": 379, "scale_edges": 324, "scale_edge_percentage": 87.33153638814017, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 5, "high_low_distance_structure": 2, "htf_confluence": 371}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_08_04_htf_rel.json", "nodes": 44, "edges": 367, "patterns": 375, "scale_edges": 326, "scale_edge_percentage": 88.8283378746594, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 5, "high_low_distance_structure": 2, "htf_confluence": 367}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_08_05_htf_regenerated_rel.json", "nodes": 34, "edges": 286, "patterns": 292, "scale_edges": 255, "scale_edge_percentage": 89.16083916083916, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 5, "high_low_distance_structure": 1, "htf_confluence": 286}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_08_05_htf_rel.json", "nodes": 34, "edges": 298, "patterns": 307, "scale_edges": 265, "scale_edge_percentage": 88.9261744966443, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 3, "htf_confluence": 298}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_08_06_htf_regenerated_rel.json", "nodes": 36, "edges": 290, "patterns": 297, "scale_edges": 259, "scale_edge_percentage": 89.3103448275862, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 4, "high_low_distance_structure": 3, "htf_confluence": 290}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "LUNCH_Lvl-1_2025_08_06_htf_rel.json", "nodes": 36, "edges": 316, "patterns": 322, "scale_edges": 282, "scale_edge_percentage": 89.24050632911393, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 4, "high_low_distance_structure": 2, "htf_confluence": 316}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_07_24_htf_regenerated_rel.json", "nodes": 15, "edges": 104, "patterns": 106, "scale_edges": 94, "scale_edge_percentage": 90.38461538461539, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "htf_confluence": 104}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_07_25_htf_regenerated_rel.json", "nodes": 17, "edges": 118, "patterns": 121, "scale_edges": 106, "scale_edge_percentage": 89.83050847457628, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "high_low_distance_structure": 2, "htf_confluence": 118}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_07_28_htf_regenerated_rel.json", "nodes": 18, "edges": 145, "patterns": 151, "scale_edges": 131, "scale_edge_percentage": 90.3448275862069, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 1, "high_low_distance_structure": 4, "htf_confluence": 145}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_07_29_htf_regenerated_rel.json", "nodes": 20, "edges": 133, "patterns": 135, "scale_edges": 118, "scale_edge_percentage": 88.7218045112782, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "htf_confluence": 133}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_07_30_htf_regenerated_rel.json", "nodes": 18, "edges": 107, "patterns": 109, "scale_edges": 94, "scale_edge_percentage": 87.85046728971963, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "high_low_distance_structure": 1, "htf_confluence": 107}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_07_31_htf_regenerated_rel.json", "nodes": 20, "edges": 133, "patterns": 136, "scale_edges": 117, "scale_edge_percentage": 87.96992481203007, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "high_low_distance_structure": 1, "htf_confluence": 133}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_08_05_htf_regenerated_rel.json", "nodes": 21, "edges": 147, "patterns": 149, "scale_edges": 129, "scale_edge_percentage": 87.75510204081633, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "htf_confluence": 147}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_08_05_htf_rel.json", "nodes": 21, "edges": 127, "patterns": 130, "scale_edges": 111, "scale_edge_percentage": 87.4015748031496, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "high_low_distance_structure": 1, "htf_confluence": 127}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_08_06_htf_regenerated_rel.json", "nodes": 18, "edges": 122, "patterns": 125, "scale_edges": 109, "scale_edge_percentage": 89.34426229508196, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "high_low_distance_structure": 1, "htf_confluence": 122}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_08_06_htf_rel.json", "nodes": 18, "edges": 128, "patterns": 131, "scale_edges": 115, "scale_edge_percentage": 89.84375, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "high_low_distance_structure": 1, "htf_confluence": 128}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_08_07_htf_regenerated_rel.json", "nodes": 15, "edges": 105, "patterns": 107, "scale_edges": 95, "scale_edge_percentage": 90.47619047619048, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "high_low_distance_structure": 1, "htf_confluence": 105}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "MIDNIGHT_Lvl-1_2025_08_07_htf_rel.json", "nodes": 15, "edges": 83, "patterns": 83, "scale_edges": 73, "scale_edge_percentage": 87.95180722891565, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"htf_confluence": 83}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NYAM_Lvl-1_2025_08_06_htf_regenerated_rel.json", "nodes": 58, "edges": 486, "patterns": 501, "scale_edges": 428, "scale_edge_percentage": 88.06584362139918, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "session_open_relationship": 1, "high_low_distance_structure": 8, "htf_confluence": 486}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NYAM_Lvl-1_2025_08_06_htf_rel.json", "nodes": 58, "edges": 531, "patterns": 544, "scale_edges": 472, "scale_edge_percentage": 88.88888888888889, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "session_open_relationship": 1, "high_low_distance_structure": 6, "htf_confluence": 531}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NYAM_Lvl-1_2025_08_07_FRESH_htf_regenerated_rel.json", "nodes": 69, "edges": 726, "patterns": 739, "scale_edges": 653, "scale_edge_percentage": 89.94490358126723, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "session_open_relationship": 1, "high_low_distance_structure": 6, "htf_confluence": 726}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NYAM_Lvl-1_2025_08_07_FRESH_htf_rel.json", "nodes": 69, "edges": 701, "patterns": 714, "scale_edges": 630, "scale_edge_percentage": 89.87161198288159, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "session_open_relationship": 1, "high_low_distance_structure": 6, "htf_confluence": 701}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NYPM_Lvl-1_2025_08_06_htf_regenerated_rel.json", "nodes": 50, "edges": 474, "patterns": 484, "scale_edges": 425, "scale_edge_percentage": 89.66244725738397, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 5, "high_low_distance_structure": 4, "htf_confluence": 474, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NYPM_Lvl-1_2025_08_06_htf_rel.json", "nodes": 50, "edges": 461, "patterns": 475, "scale_edges": 414, "scale_edge_percentage": 89.80477223427332, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 7, "htf_confluence": 461, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_07_24_htf_regenerated_rel.json", "nodes": 17, "edges": 107, "patterns": 109, "scale_edges": 95, "scale_edge_percentage": 88.78504672897196, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "htf_confluence": 107}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_07_25_htf_regenerated_rel.json", "nodes": 18, "edges": 125, "patterns": 130, "scale_edges": 112, "scale_edge_percentage": 89.60000000000001, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 1, "high_low_distance_structure": 3, "htf_confluence": 125}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_07_28_htf_regenerated_rel.json", "nodes": 27, "edges": 214, "patterns": 219, "scale_edges": 192, "scale_edge_percentage": 89.7196261682243, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 3, "high_low_distance_structure": 1, "htf_confluence": 214}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_07_29_htf_regenerated_rel.json", "nodes": 44, "edges": 388, "patterns": 397, "scale_edges": 347, "scale_edge_percentage": 89.43298969072166, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 5, "session_open_relationship": 1, "high_low_distance_structure": 3, "htf_confluence": 388}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_07_30_htf_regenerated_rel.json", "nodes": 28, "edges": 227, "patterns": 231, "scale_edges": 203, "scale_edge_percentage": 89.42731277533039, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 3, "high_low_distance_structure": 1, "htf_confluence": 227}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_07_31_htf_regenerated_rel.json", "nodes": 27, "edges": 216, "patterns": 221, "scale_edges": 194, "scale_edge_percentage": 89.81481481481481, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "session_open_relationship": 1, "high_low_distance_structure": 2, "htf_confluence": 216}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_08_04_htf_regenerated_rel.json", "nodes": 108, "edges": 1424, "patterns": 1435, "scale_edges": 1310, "scale_edge_percentage": 91.99438202247191, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 5, "session_open_relationship": 1, "high_low_distance_structure": 4, "htf_confluence": 1424}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_08_04_htf_rel.json", "nodes": 108, "edges": 1519, "patterns": 1533, "scale_edges": 1388, "scale_edge_percentage": 91.37590520079, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 5, "session_open_relationship": 1, "high_low_distance_structure": 7, "htf_confluence": 1519}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_08_05_htf_regenerated_rel.json", "nodes": 64, "edges": 658, "patterns": 670, "scale_edges": 588, "scale_edge_percentage": 89.36170212765957, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "session_open_relationship": 1, "high_low_distance_structure": 5, "htf_confluence": 658}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_AM_Lvl-1_2025_08_05_htf_rel.json", "nodes": 64, "edges": 662, "patterns": 672, "scale_edges": 591, "scale_edge_percentage": 89.27492447129909, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "session_open_relationship": 1, "high_low_distance_structure": 3, "htf_confluence": 662}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1_2025_07_28_htf_regenerated_rel.json", "nodes": 24, "edges": 177, "patterns": 185, "scale_edges": 155, "scale_edge_percentage": 87.57062146892656, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 5, "high_low_distance_structure": 1, "htf_confluence": 177, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1_2025_07_29_htf_regenerated_rel.json", "nodes": 40, "edges": 300, "patterns": 311, "scale_edges": 263, "scale_edge_percentage": 87.66666666666667, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 5, "high_low_distance_structure": 5, "htf_confluence": 300, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1_2025_07_30_htf_regenerated_rel.json", "nodes": 46, "edges": 359, "patterns": 369, "scale_edges": 318, "scale_edge_percentage": 88.57938718662952, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 4, "session_open_relationship": 2, "high_low_distance_structure": 3, "htf_confluence": 359, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1_2025_08_04_htf_regenerated_rel.json", "nodes": 31, "edges": 260, "patterns": 266, "scale_edges": 234, "scale_edge_percentage": 90.0, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 3, "high_low_distance_structure": 1, "htf_confluence": 260, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1_2025_08_04_htf_rel.json", "nodes": 31, "edges": 258, "patterns": 265, "scale_edges": 231, "scale_edge_percentage": 89.53488372093024, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 3, "high_low_distance_structure": 2, "htf_confluence": 258, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1_2025_08_05_htf_regenerated_rel.json", "nodes": 71, "edges": 725, "patterns": 739, "scale_edges": 656, "scale_edge_percentage": 90.48275862068965, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 7, "htf_confluence": 725, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1_2025_08_05_htf_rel.json", "nodes": 71, "edges": 820, "patterns": 833, "scale_edges": 742, "scale_edge_percentage": 90.48780487804878, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 6, "htf_confluence": 820, "temporal_structural_position": 1}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1__htf_regenerated_rel.json", "nodes": 32, "edges": 263, "patterns": 269, "scale_edges": 232, "scale_edge_percentage": 88.212927756654, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 5, "high_low_distance_structure": 1, "htf_confluence": 263}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "NY_PM_Lvl-1__htf_rel.json", "nodes": 32, "edges": 238, "patterns": 242, "scale_edges": 210, "scale_edge_percentage": 88.23529411764706, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 4, "htf_confluence": 238}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREASIA_Lvl-1_2025_07_30_htf_regenerated_rel.json", "nodes": 13, "edges": 83, "patterns": 85, "scale_edges": 75, "scale_edge_percentage": 90.36144578313254, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "high_low_distance_structure": 1, "htf_confluence": 83}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_07_24_htf_regenerated_rel.json", "nodes": 15, "edges": 106, "patterns": 107, "scale_edges": 95, "scale_edge_percentage": 89.62264150943396, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 1, "htf_confluence": 106}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_07_25_htf_regenerated_rel.json", "nodes": 18, "edges": 107, "patterns": 109, "scale_edges": 94, "scale_edge_percentage": 87.85046728971963, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "high_low_distance_structure": 1, "htf_confluence": 107}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_07_28_htf_regenerated_rel.json", "nodes": 21, "edges": 142, "patterns": 148, "scale_edges": 125, "scale_edge_percentage": 88.02816901408451, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"weekly_cycle_pattern": 1, "range_position_confluence": 2, "high_low_distance_structure": 3, "htf_confluence": 142}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_07_29_htf_regenerated_rel.json", "nodes": 27, "edges": 182, "patterns": 185, "scale_edges": 160, "scale_edge_percentage": 87.91208791208791, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "high_low_distance_structure": 1, "htf_confluence": 182}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_07_30_htf_regenerated_rel.json", "nodes": 31, "edges": 256, "patterns": 260, "scale_edges": 227, "scale_edge_percentage": 88.671875, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "high_low_distance_structure": 2, "htf_confluence": 256}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_07_31_htf_regenerated_rel.json", "nodes": 27, "edges": 252, "patterns": 257, "scale_edges": 229, "scale_edge_percentage": 90.87301587301587, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 2, "high_low_distance_structure": 3, "htf_confluence": 252}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_08_05_htf_regenerated_rel.json", "nodes": 47, "edges": 401, "patterns": 411, "scale_edges": 353, "scale_edge_percentage": 88.02992518703242, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 4, "htf_confluence": 401}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_08_05_htf_rel.json", "nodes": 47, "edges": 422, "patterns": 433, "scale_edges": 373, "scale_edge_percentage": 88.38862559241706, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 6, "high_low_distance_structure": 5, "htf_confluence": 422}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_08_06_htf_regenerated_rel.json", "nodes": 32, "edges": 240, "patterns": 248, "scale_edges": 212, "scale_edge_percentage": 88.33333333333333, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 5, "high_low_distance_structure": 3, "htf_confluence": 240}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_08_06_htf_rel.json", "nodes": 32, "edges": 261, "patterns": 268, "scale_edges": 230, "scale_edge_percentage": 88.12260536398468, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 5, "high_low_distance_structure": 2, "htf_confluence": 261}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_08_07_htf_regenerated_rel.json", "nodes": 27, "edges": 206, "patterns": 215, "scale_edges": 183, "scale_edge_percentage": 88.83495145631069, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 4, "high_low_distance_structure": 5, "htf_confluence": 206}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}, {"session_name": "PREMARKET_Lvl-1_2025_08_07_htf_rel.json", "nodes": 27, "edges": 217, "patterns": 223, "scale_edges": 193, "scale_edge_percentage": 88.94009216589862, "timeframes": ["1m", "15m", "5m", "1h", "D"], "pattern_breakdown": {"range_position_confluence": 3, "high_low_distance_structure": 3, "htf_confluence": 217}, "node_feature_dims": 37, "edge_feature_dims": 17, "success": true}]}